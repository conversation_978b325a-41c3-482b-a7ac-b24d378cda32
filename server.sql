-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 4.8.5
-- https://www.phpmyadmin.net/
--
-- 主机： localhost
-- 生成日期： 2025-08-01 19:14:51
-- 服务器版本： 5.7.26
-- PHP 版本： 7.3.4

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
SET AUTOCOMMIT = 0;
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- 数据库： `ppanel`
--

-- --------------------------------------------------------

--
-- 表的结构 `server`
--

CREATE TABLE `server` (
  `id` bigint(20) NOT NULL,
  `name` varchar(100) NOT NULL DEFAULT '' COMMENT 'Node Name',
  `tags` varchar(128) NOT NULL DEFAULT '' COMMENT 'Tags',
  `country` varchar(128) NOT NULL DEFAULT '' COMMENT 'Country',
  `city` varchar(128) NOT NULL DEFAULT '' COMMENT 'City',
  `latitude` varchar(128) NOT NULL DEFAULT '' COMMENT 'latitude',
  `longitude` varchar(128) NOT NULL DEFAULT '' COMMENT 'longitude',
  `server_addr` varchar(100) NOT NULL DEFAULT '' COMMENT 'Server Address',
  `relay_mode` varchar(20) NOT NULL DEFAULT 'none' COMMENT 'Relay Mode',
  `relay_node` text COMMENT 'Relay Node',
  `speed_limit` bigint(20) NOT NULL DEFAULT '0' COMMENT 'Speed Limit',
  `traffic_ratio` decimal(4,2) NOT NULL DEFAULT '0.00' COMMENT 'Traffic Ratio',
  `group_id` bigint(20) DEFAULT NULL COMMENT 'Group ID',
  `protocol` varchar(20) NOT NULL DEFAULT '' COMMENT 'Protocol',
  `config` text COMMENT 'Config',
  `enable` tinyint(1) NOT NULL DEFAULT '1' COMMENT 'Enabled',
  `sort` bigint(20) NOT NULL DEFAULT '0' COMMENT 'Sort',
  `last_reported_at` datetime(3) DEFAULT NULL COMMENT 'Last Reported Time',
  `created_at` datetime(3) DEFAULT NULL COMMENT 'Creation Time',
  `updated_at` datetime(3) DEFAULT NULL COMMENT 'Update Time'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

--
-- 转存表中的数据 `server`
--

INSERT INTO `server` (`id`, `name`, `tags`, `country`, `city`, `latitude`, `longitude`, `server_addr`, `relay_mode`, `relay_node`, `speed_limit`, `traffic_ratio`, `group_id`, `protocol`, `config`, `enable`, `sort`, `last_reported_at`, `created_at`, `updated_at`) VALUES
(1, '1', '', 'AU', 'Brisbane', '-27.4816', '153.0175', '*******', 'all', '[{\"host\":\"*******\",\"port\":8443,\"prefix\":\"\"}]', 52428800, '1.00', 1, 'tuic', '{\"port\":443,\"security\":\"tls\",\"security_config\":{\"allow_insecure\":true}}', 1, 1, '1970-01-01 00:20:18.125', '2025-08-01 11:13:13.890', '2025-08-01 11:14:03.734');

--
-- 转储表的索引
--

--
-- 表的索引 `server`
--
ALTER TABLE `server`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_group_id` (`group_id`);

--
-- 在导出的表使用AUTO_INCREMENT
--

--
-- 使用表AUTO_INCREMENT `server`
--
ALTER TABLE `server`
  MODIFY `id` bigint(20) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
