import React, { useEffect, useState } from 'react';
import {
  Form,
  Input,
  InputNumber,
  Select,
  Button,
  Space,
  Row,
  Col,
  Card,
  Divider,
  Tooltip,
  Switch,
  Tag,
  message,
  Tabs,
  Alert,
  Typography,
} from 'antd';
import {
  InfoCircleOutlined,
  CloudServerOutlined,
  SettingOutlined,
  LockOutlined,
  GlobalOutlined,
  ThunderboltOutlined,
  TeamOutlined,
  EyeInvisibleOutlined,
  EyeOutlined,
  SwapOutlined,
  QuestionCircleOutlined,

} from '@ant-design/icons';
import {
  useCreateNode,
  useUpdateNode,
  nodeService,
  ProtocolTemplates,
  type Node,
  type NodeForm as NodeFormData,
  type NodeUpdateForm,
  type AnyTLSConfig,
} from '../../../../services/node';
import { useAllPermissionGroups } from '../../../../services/permissionGroup';

const { Option } = Select;
const { TextArea } = Input;
const { TabPane } = Tabs;
const { Text } = Typography;



interface NodeFormProps {
  initialValues?: Node | null;
  onSuccess?: () => void;
  onCancel?: () => void;
}

const NodeForm: React.FC<NodeFormProps> = ({
  initialValues,
  onSuccess,
  onCancel,
}) => {
  const [form] = Form.useForm();
  const [selectedProtocol, setSelectedProtocol] = useState<string>('anytls');
  const [showPassword, setShowPassword] = useState(false);
  const [configValid, setConfigValid] = useState(true);
  const [configErrors, setConfigErrors] = useState<string[]>([]);
  
  const isEdit = initialValues && initialValues.id > 0;

  // 获取权限组数据
  const { data: permissionGroups = [], isLoading: groupsLoading } = useAllPermissionGroups();

  // 表单提交
  const createNode = useCreateNode();
  const updateNode = useUpdateNode();



  // 设置初始值
  useEffect(() => {
    if (initialValues) {
      // 确保config有默认值
      const defaultConfig = nodeService.generateDefaultConfig(initialValues.protocol);
      const config = initialValues.config && typeof initialValues.config === 'object'
        ? initialValues.config
        : defaultConfig;

      const formValues = {
        name: initialValues.name,
        protocol: initialValues.protocol,
        host: initialValues.host,
        port: initialValues.port,
        relay_host: initialValues.relay_host,
        relay_port: initialValues.relay_port,

        server_name: initialValues.server_name,
        status: initialValues.status,
        sort_order: initialValues.sort_order,
        traffic_rate: initialValues.traffic_rate,
        max_users: initialValues.max_users,
        config: config,
        group_ids: initialValues.group_ids || [],
      };
      form.setFieldsValue(formValues);
      setSelectedProtocol(initialValues.protocol);
    } else {
      // 新建节点时的默认值
      const defaultConfig = nodeService.generateDefaultConfig('anytls');
      form.setFieldsValue({
        protocol: 'anytls',
        status: 'online',
        sort_order: 0,
        traffic_rate: 1.0,
        max_users: 0,
        config: defaultConfig,
      });
      setSelectedProtocol('anytls');
    }
  }, [initialValues, form]);

  // 协议变更处理
  const handleProtocolChange = (protocol: string) => {
    setSelectedProtocol(protocol);
    const defaultConfig = nodeService.generateDefaultConfig(protocol);
    form.setFieldsValue({ config: defaultConfig });
    validateConfig(protocol, defaultConfig);
  };

  // 配置验证
  const validateConfig = (protocol: string, config: any) => {
    const validation = nodeService.validateNodeConfig(protocol, config);
    setConfigValid(validation.valid);
    setConfigErrors(validation.errors);
  };

  // 配置变更处理
  const handleConfigChange = (changedValues: any, allValues: any) => {
    if (changedValues.config) {
      validateConfig(selectedProtocol, changedValues.config);
    }
  };

  // 表单提交处理
  const handleSubmit = async (values: any) => {
    // 确保配置存在，如果不存在则使用默认配置
    const config = values.config || nodeService.generateDefaultConfig(values.protocol);

    // 验证配置
    const validation = nodeService.validateNodeConfig(values.protocol, config);
    if (!validation.valid) {
      message.error(`节点配置无效：${validation.errors.join(', ')}`);
      return;
    }

    try {
      const formData = {
        ...values,
        config: JSON.stringify(config),
      };

      if (isEdit && initialValues) {
        // 编辑模式
        const updateData: NodeUpdateForm = { ...formData };
        await updateNode.mutateAsync({ 
          id: initialValues.id, 
          data: updateData 
        });
      } else {
        // 创建模式
        await createNode.mutateAsync(formData as NodeFormData);
      }
      
      onSuccess?.();
    } catch (error) {
      console.error('表单提交失败:', error);
    }
  };

  // 重置配置为默认值
  const resetToDefault = () => {
    const defaultConfig = nodeService.generateDefaultConfig(selectedProtocol);
    form.setFieldsValue({ config: defaultConfig });
    validateConfig(selectedProtocol, defaultConfig);
  };

  // 预设端口号
  const portPresets = [
    { label: '443 (HTTPS)', value: 443 },
    { label: '80 (HTTP)', value: 80 },
    { label: '8080 (Alt HTTP)', value: 8080 },
    { label: '2053 (常用)', value: 2053 },
    { label: '2083 (常用)', value: 2083 },
    { label: '2087 (常用)', value: 2087 },
    { label: '2096 (常用)', value: 2096 },
  ];

  const loading = createNode.isPending || updateNode.isPending;
  const protocolTemplate = ProtocolTemplates[selectedProtocol as keyof typeof ProtocolTemplates] || ProtocolTemplates.anytls;

  return (
    <Form
      form={form}
      layout="vertical"
      onFinish={handleSubmit}
      onValuesChange={handleConfigChange}
      initialValues={{
        protocol: 'anytls',
        status: 'online',
        sort_order: 0,
        traffic_rate: 1.0,
        max_users: 0,
      }}
    >
      <Tabs defaultActiveKey="basic" type="card">
        {/* 基础配置 */}
        <TabPane 
          tab={
            <Space>
              <InfoCircleOutlined />
              基础配置
            </Space>
          } 
          key="basic"
        >
          <Row gutter={24}>
            <Col span={24}>
              <Card 
                size="small" 
                title={
                  <Space>
                    <CloudServerOutlined />
                    节点信息
                  </Space>
                }
              >
                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item
                      name="name"
                      label="节点名称"
                      rules={[
                        { required: true, message: '请输入节点名称' },
                        { min: 1, max: 100, message: '节点名称长度为1-100个字符' },
                      ]}
                    >
                      <Input 
                        prefix={<CloudServerOutlined />} 
                        placeholder="请输入节点名称" 
                      />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      name="protocol"
                      label="协议类型"
                      rules={[{ required: true, message: '请选择协议类型' }]}
                    >
                      <Select 
                        placeholder="请选择协议类型"
                        onChange={handleProtocolChange}
                      >
                        {Object.entries(ProtocolTemplates).map(([key, template]) => (
                          <Option key={key} value={key}>
                            <Space>
                              <span>{template.icon}</span>
                              <span>{template.name}</span>
                              <Text type="secondary" style={{ fontSize: '12px' }}>
                                {template.description}
                              </Text>
                            </Space>
                          </Option>
                        ))}
                      </Select>
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      name="host"
                      label="服务器地址"
                      rules={[
                        { required: true, message: '请输入服务器地址' },
                        { pattern: /^[a-zA-Z0-9.-]+$/, message: '请输入有效的域名或IP地址' },
                      ]}
                    >
                      <Input 
                        prefix={<GlobalOutlined />} 
                        placeholder="example.com 或 *******" 
                      />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      name="port"
                      label="端口号"
                      rules={[
                        { required: true, message: '请输入端口号' },
                        { type: 'number', min: 1, max: 65535, message: '端口号范围为1-65535' },
                      ]}
                    >
                      <InputNumber
                        style={{ width: '100%' }}
                        placeholder="请输入端口号"
                        min={1}
                        max={65535}
                      />
                    </Form.Item>
                    <div style={{ marginTop: '-16px', marginBottom: '16px' }}>
                      <Space wrap>
                        {portPresets.map((preset) => (
                          <Button
                            key={preset.value}
                            size="small"
                            type="dashed"
                            onClick={() => form.setFieldsValue({ port: preset.value })}
                          >
                            {preset.label}
                          </Button>
                        ))}
                      </Space>
                    </div>
                  </Col>
                </Row>

                {/* 权限组选择 */}
                <Row gutter={16} style={{ marginTop: '-16px' }}>
                  <Col span={12}>
                    <Form.Item
                      name="group_ids"
                      label="权限组"
                      tooltip="选择节点所属的权限组，可以选择多个"
                    >
                      <Select
                        mode="multiple"
                        placeholder="请选择权限组"
                        loading={groupsLoading}
                        allowClear
                        showSearch
                        filterOption={(input, option) =>
                          (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                        }
                        options={permissionGroups.map(group => ({
                          value: group.id,
                          label: group.name,
                        }))}
                      />
                    </Form.Item>
                  </Col>
                </Row>
              </Card>
            </Col>

            {/* 中转模式配置 */}
            <Col span={24}>
              <Card
                size="small"
                title={
                  <Space>
                    <SwapOutlined />
                    中转模式
                    <Tooltip title="可选配置，如果设置了中转地址和端口，订阅时将使用中转服务器替代原始服务器">
                      <QuestionCircleOutlined style={{ color: '#999' }} />
                    </Tooltip>
                  </Space>
                }
              >
                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item
                      name="relay_host"
                      label="中转地址"
                      tooltip="可选，用于中转的服务器地址"
                    >
                      <Input
                        placeholder="例如：relay.example.com（可选）"
                        allowClear
                      />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      name="relay_port"
                      label="中转端口"
                      tooltip="可选，用于中转的端口号"
                    >
                      <InputNumber
                        style={{ width: '100%' }}
                        placeholder="例如：443（可选）"
                        min={1}
                        max={65535}
                      />
                    </Form.Item>
                  </Col>
                </Row>
              </Card>
            </Col>

            <Col span={24}>
              <Card
                size="small"
                title={
                  <Space>
                    <SettingOutlined />
                    运行配置
                  </Space>
                }
              >
                <Row gutter={16}>
                  <Col span={8}>
                    <Form.Item
                      name="status"
                      label="初始状态"
                      rules={[{ required: true, message: '请选择初始状态' }]}
                    >
                      <Select placeholder="请选择初始状态">
                        <Option value="online">
                          <Tag color="success">在线</Tag>
                        </Option>
                        <Option value="offline">
                          <Tag color="error">离线</Tag>
                        </Option>
                        <Option value="maintenance">
                          <Tag color="warning">维护</Tag>
                        </Option>
                      </Select>
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item
                      name="traffic_rate"
                      label={
                        <Space>
                          流量倍率
                          <Tooltip title="用户流量消耗倍率，1.0表示正常计费">
                            <InfoCircleOutlined />
                          </Tooltip>
                        </Space>
                      }
                      rules={[
                        { required: true, message: '请输入流量倍率' },
                        { type: 'number', min: 0, message: '流量倍率不能小于0' },
                      ]}
                    >
                      <InputNumber
                        style={{ width: '100%' }}
                        placeholder="1.0"
                        min={0}
                        step={0.1}
                        precision={1}
                        addonAfter="x"
                      />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item
                      name="max_users"
                      label={
                        <Space>
                          最大用户数
                          <Tooltip title="0表示无限制">
                            <InfoCircleOutlined />
                          </Tooltip>
                        </Space>
                      }
                      rules={[
                        { required: true, message: '请输入最大用户数' },
                        { type: 'number', min: 0, message: '最大用户数不能小于0' },
                      ]}
                    >
                      <InputNumber
                        style={{ width: '100%' }}
                        placeholder="0"
                        min={0}
                        addonAfter="人"
                      />
                    </Form.Item>
                  </Col>
                  <Col span={24}>
                    <Form.Item
                      name="sort_order"
                      label={
                        <Space>
                          排序权重
                          <Tooltip title="数值越小优先级越高">
                            <InfoCircleOutlined />
                          </Tooltip>
                        </Space>
                      }
                    >
                      <InputNumber
                        style={{ width: '100%' }}
                        placeholder="0"
                        min={0}
                      />
                    </Form.Item>
                  </Col>
                </Row>
              </Card>
            </Col>
          </Row>
        </TabPane>

        {/* 协议配置 */}
        <TabPane 
          tab={
            <Space>
              <SettingOutlined />
              {protocolTemplate.name}配置
            </Space>
          } 
          key="protocol"
        >
          <div>
            {!configValid && (
              <Alert
                type="error"
                showIcon
                message="配置错误"
                description={
                  <ul style={{ margin: 0, paddingLeft: '20px' }}>
                    {configErrors.map((error, index) => (
                      <li key={index}>{error}</li>
                    ))}
                  </ul>
                }
                style={{ marginBottom: '16px' }}
              />
            )}

            <div style={{ marginBottom: '16px', textAlign: 'right' }}>
              <Button type="dashed" onClick={resetToDefault}>
                重置为默认配置
              </Button>
            </div>

            <Form.Item
              name="config"
              label={`${protocolTemplate.name} 协议配置`}
              initialValue={protocolTemplate.defaultConfig}
            >
              <ProtocolConfigEditor
                protocol={selectedProtocol}
                template={protocolTemplate}
                onChange={(config) => {
                  form.setFieldsValue({ config });
                  validateConfig(selectedProtocol, config);
                }}
              />
            </Form.Item>
          </div>
        </TabPane>
      </Tabs>

      <Divider />

      <div style={{ textAlign: 'right' }}>
        <Space>
          <Button onClick={onCancel}>
            取消
          </Button>
          <Button 
            type="primary" 
            htmlType="submit" 
            loading={loading}
            disabled={!configValid}
          >
            {isEdit ? '更新节点' : '创建节点'}
          </Button>
        </Space>
      </div>
    </Form>
  );
};

// 协议配置编辑器组件
interface ProtocolConfigEditorProps {
  protocol: string;
  template: any;
  onChange: (config: any) => void;
  value?: any;
}

const ProtocolConfigEditor: React.FC<ProtocolConfigEditorProps> = ({
  protocol,
  template,
  onChange,
  value = {},
}) => {
  // 确保config始终有一个有效的默认值
  const defaultConfig = template?.defaultConfig || {};
  const [config, setConfig] = useState(value || defaultConfig);

  useEffect(() => {
    const newConfig = value || defaultConfig;
    setConfig(newConfig);
    // 如果value为null或undefined，通知父组件使用默认配置
    if (!value && onChange) {
      onChange(newConfig);
    }
  }, [value, defaultConfig, onChange]);

  const handleFieldChange = (path: string[], fieldValue: any) => {
    const newConfig = { ...(config || {}) };
    let current = newConfig;
    
    // 导航到嵌套对象
    for (let i = 0; i < path.length - 1; i++) {
      if (!current[path[i]]) {
        current[path[i]] = {};
      }
      current = current[path[i]];
    }
    
    // 设置值
    current[path[path.length - 1]] = fieldValue;
    
    setConfig(newConfig);
    onChange?.(newConfig);
  };

  const renderField = (field: any, parentPath: string[] = []) => {
    const fieldPath = [...parentPath, field.name];
    const fieldKey = fieldPath.join('.');
    
    // 获取当前值
    let currentValue = config || {};
    for (const key of fieldPath) {
      currentValue = currentValue?.[key];
    }

    switch (field.type) {
      case 'boolean':
        return (
          <div key={fieldKey} style={{ marginBottom: '16px' }}>
            <label style={{ display: 'block', marginBottom: '4px', fontWeight: 'bold' }}>
              {field.label}
            </label>
            <Switch
              checked={currentValue || false}
              onChange={(checked) => handleFieldChange(fieldPath, checked)}
            />
          </div>
        );

      case 'number':
        return (
          <div key={fieldKey} style={{ marginBottom: '16px' }}>
            <label style={{ display: 'block', marginBottom: '4px', fontWeight: 'bold' }}>
              {field.label}
            </label>
            {field.description && (
              <div style={{ fontSize: '12px', color: '#666', marginBottom: '4px' }}>
                {field.description}
              </div>
            )}
            <InputNumber
              style={{ width: '100%' }}
              value={currentValue}
              min={field.min}
              max={field.max}
              step={field.step || 1}
              onChange={(val) => handleFieldChange(fieldPath, val)}
              placeholder={field.placeholder || `请输入${field.label}`}
              addonAfter={field.suffix}
            />
          </div>
        );

      case 'string':
        return (
          <div key={fieldKey} style={{ marginBottom: '16px' }}>
            <label style={{ display: 'block', marginBottom: '4px', fontWeight: 'bold' }}>
              {field.label}
            </label>
            {field.description && (
              <div style={{ fontSize: '12px', color: '#666', marginBottom: '4px' }}>
                {field.description}
              </div>
            )}
            <Input
              value={currentValue || ''}
              onChange={(e) => handleFieldChange(fieldPath, e.target.value)}
              placeholder={field.placeholder || `请输入${field.label}`}
            />
          </div>
        );

      case 'select':
        return (
          <div key={fieldKey} style={{ marginBottom: '16px' }}>
            <label style={{ display: 'block', marginBottom: '4px', fontWeight: 'bold' }}>
              {field.label}
            </label>
            <Select
              style={{ width: '100%' }}
              value={currentValue}
              onChange={(val) => handleFieldChange(fieldPath, val)}
              placeholder={`请选择${field.label}`}
            >
              {field.options?.map((option: any) => (
                <Option key={option.value} value={option.value}>
                  {option.label}
                </Option>
              ))}
            </Select>
          </div>
        );

      case 'tags':
        return (
          <div key={fieldKey} style={{ marginBottom: '16px' }}>
            <label style={{ display: 'block', marginBottom: '4px', fontWeight: 'bold' }}>
              {field.label}
            </label>
            <Select
              mode="tags"
              style={{ width: '100%' }}
              value={currentValue || []}
              onChange={(val) => handleFieldChange(fieldPath, val)}
              placeholder={`请输入${field.label}`}
            />
          </div>
        );

      case 'object':
        return (
          <Card key={fieldKey} size="small" title={field.label} style={{ marginBottom: '16px' }}>
            {field.fields?.map((subField: any) => renderField(subField, fieldPath))}
          </Card>
        );

      default:
        return null;
    }
  };

  // 渲染协议特定的配置
  const renderProtocolConfig = () => {
    if (!template || !template.configFields || template.configFields.length === 0) {
      return (
        <Card>
          <div style={{ textAlign: 'center', padding: '40px', color: '#999' }}>
            <SettingOutlined style={{ fontSize: '48px', marginBottom: '16px' }} />
            <div>该协议使用默认配置</div>
            <div style={{ marginTop: '8px', fontSize: '12px' }}>
              {template?.name || protocol} 协议配置
            </div>
          </div>
        </Card>
      );
    }

    return (
      <div>
        {template.configFields.map((field: any) => renderField(field))}
      </div>
    );
  };

  return renderProtocolConfig();
};

export default NodeForm;